# 抖音自动加好友脚本 - Auto.js 6

## 项目简介

这是一个基于 Auto.js 6 开发的抖音自动化脚本，用于技术面试任务演示。脚本可以自动完成以下操作：

1. ✅ 启动抖音App（假设无后台运行状态）
2. ✅ 自动跳转至"添加好友"页面
3. ✅ 搜索并添加指定抖音号
4. ✅ 添加成功后，进入该用户主页
5. ✅ 点击"私信"，发送固定内容消息

## 技术特性

- 🚀 使用 Auto.js 6 开发
- 🛡️ 完善的异常处理机制
- 📱 支持 Android 10 及以上系统
- 🔍 智能UI元素识别
- 📝 详细的日志记录
- ⚡ 自动重试机制

## 系统要求

- Android 10+ 系统
- Auto.js 6 (Pro版或开源版本)
- 已安装抖音应用
- 开启无障碍服务权限
- 授予截图权限

## 快速开始

### 1. 配置目标抖音号

编辑 `main.js` 文件中的配置：

```javascript
const CONFIG = {
    // 修改为目标抖音号
    TARGET_DOUYIN_ID: "your_target_douyin_id",
    
    // 可自定义私信内容
    MESSAGE_CONTENT: "你好，我看了你的作品觉得非常喜欢，可以交个朋友吗"
};
```

### 2. 运行脚本

1. 将 `main.js` 导入到 Auto.js 6 中
2. 确保已开启无障碍服务
3. 点击运行按钮
4. 按提示授予截图权限
5. 脚本将自动执行完整流程

## 异常处理

脚本包含以下异常处理机制：

### 账号状态处理
- ❌ **账号不存在** - 检测到"暂无相关用户"时停止执行
- ✅ **已添加好友** - 检测到"已关注"状态时跳过添加步骤
- 🔄 **网络异常** - 自动重试机制，最多重试3次

### UI适配处理
- 🎯 **多选择器匹配** - 支持文本、描述、ID多种匹配方式
- ⏱️ **智能等待** - 动态等待UI元素加载完成
- 🔧 **版本兼容** - 适配不同版本的抖音UI

## 核心功能模块

### 1. 应用启动模块
```javascript
launchDouyin() {
    // 检查应用状态
    // 启动抖音应用
    // 处理启动弹窗
}
```

### 2. 导航模块
```javascript
navigateToAddFriend() {
    // 点击"我"标签
    // 找到"添加好友"入口
    // 进入添加好友页面
}
```

### 3. 搜索模块
```javascript
searchUser(douyinId) {
    // 定位搜索框
    // 输入抖音号
    // 执行搜索操作
}
```

### 4. 添加好友模块
```javascript
addFriend() {
    // 检查用户状态
    // 点击关注按钮
    // 验证关注结果
}
```

### 5. 私信模块
```javascript
sendPrivateMessage() {
    // 进入用户主页
    // 点击私信按钮
    // 发送消息内容
}
```

## 工具函数

### 智能点击
```javascript
Utils.smartClick(selector, retries = 3)
```
支持多种选择器类型，自动重试机制

### 智能输入
```javascript
Utils.smartInput(selector, text)
```
智能定位输入框，自动输入文本

### 元素等待
```javascript
Utils.waitForElement(selector, timeout = 10000)
```
动态等待UI元素出现

## 调试功能

### 日志系统
- 控制台日志输出
- Toast消息提示
- 时间戳记录
- 不同级别日志（DEBUG, INFO, WARN, ERROR）

### 调试模式
设置 `CONFIG.DEBUG = true` 开启详细日志

## 注意事项

### 使用前准备
1. 确保抖音应用已安装且可正常使用
2. 开启Auto.js无障碍服务权限
3. 授予截图权限
4. 修改目标抖音号配置

### 使用建议
1. 首次使用建议开启调试模式
2. 根据网络情况调整等待时间
3. 避免频繁使用以防被平台限制
4. 仅用于技术学习和研究

### 法律声明
- 本脚本仅供技术面试和学习使用
- 请遵守相关平台的使用条款
- 不得用于商业用途或恶意行为
- 使用者需自行承担使用风险

## 故障排除

### 常见问题

**Q: 脚本无法启动？**
A: 检查无障碍服务是否开启，确认Auto.js版本兼容性

**Q: 找不到UI元素？**
A: 开启调试模式查看日志，可能需要调整选择器或等待时间

**Q: 操作失败？**
A: 检查网络连接，增加等待时间，查看错误日志

**Q: 权限问题？**
A: 确保已授予无障碍服务和截图权限

## 技术实现亮点

1. **模块化设计** - 清晰的类结构和功能分离
2. **异常处理** - 完善的错误捕获和处理机制
3. **智能识别** - 多种UI元素识别方式
4. **自动重试** - 网络异常和操作失败自动重试
5. **日志系统** - 详细的执行日志和调试信息

## 版本信息

- **版本**: v1.0.0
- **开发环境**: Auto.js 6
- **目标系统**: Android 10+
- **测试设备**: 通用Android设备

---

**免责声明**: 本脚本仅供技术学习和面试演示使用，请勿用于违反平台规则的行为。
