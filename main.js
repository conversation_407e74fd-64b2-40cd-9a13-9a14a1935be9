/**
 * 抖音自动加好友脚本 - Auto.js 6
 * 技术面试任务实现
 * 
 * 功能：
 * 1. 启动抖音App
 * 2. 跳转至添加好友页面  
 * 3. 搜索并添加指定抖音号
 * 4. 进入用户主页
 * 5. 发送私信
 */

"ui";

// 配置参数
const CONFIG = {
    // 目标抖音号 - 请修改为实际的抖音号
    TARGET_DOUYIN_ID: "test_douyin_id",
    
    // 私信内容
    MESSAGE_CONTENT: "你好，我看了你的作品觉得非常喜欢，可以交个朋友吗",
    
    // 抖音包名
    DOUYIN_PACKAGE: "com.ss.android.ugc.aweme",
    
    // 等待时间配置（毫秒）
    WAIT_TIMES: {
        APP_LAUNCH: 5000,
        PAGE_LOAD: 3000,
        SEARCH_RESULT: 2000,
        BUTTON_CLICK: 1000,
        INPUT_DELAY: 500,
        NETWORK_REQUEST: 3000
    },
    
    // 调试模式
    DEBUG: true
};

// 工具函数类
class Utils {
    /**
     * 日志输出
     */
    static log(level, message) {
        if (CONFIG.DEBUG) {
            const timestamp = new Date().toLocaleString();
            console.log(`[${timestamp}] [${level}] ${message}`);
            if (level === 'INFO') {
                toast(message);
            }
        }
    }

    /**
     * 等待指定时间
     */
    static sleep(ms) {
        this.log('DEBUG', `等待 ${ms}ms`);
        sleep(ms);
    }

    /**
     * 智能等待元素出现
     */
    static waitForElement(selector, timeout = 10000) {
        this.log('DEBUG', `等待元素: ${selector}`);
        
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            if (textContains(selector).exists() || 
                descContains(selector).exists() || 
                idContains(selector).exists()) {
                this.log('DEBUG', `找到元素: ${selector}`);
                return true;
            }
            sleep(500);
        }
        
        this.log('WARN', `元素未找到: ${selector}`);
        return false;
    }

    /**
     * 智能点击元素
     */
    static smartClick(selector, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                // 尝试通过文本点击
                if (textContains(selector).exists()) {
                    textContains(selector).click();
                    this.log('INFO', `点击成功: ${selector}`);
                    this.sleep(CONFIG.WAIT_TIMES.BUTTON_CLICK);
                    return true;
                }
                
                // 尝试通过描述点击
                if (descContains(selector).exists()) {
                    descContains(selector).click();
                    this.log('INFO', `点击成功: ${selector}`);
                    this.sleep(CONFIG.WAIT_TIMES.BUTTON_CLICK);
                    return true;
                }
                
                // 尝试通过ID点击
                if (idContains(selector).exists()) {
                    idContains(selector).click();
                    this.log('INFO', `点击成功: ${selector}`);
                    this.sleep(CONFIG.WAIT_TIMES.BUTTON_CLICK);
                    return true;
                }
                
                this.log('WARN', `第${i+1}次点击失败: ${selector}`);
                this.sleep(1000);
                
            } catch (error) {
                this.log('ERROR', `点击异常: ${selector}, 错误: ${error.message}`);
            }
        }
        
        this.log('ERROR', `点击失败: ${selector}`);
        return false;
    }

    /**
     * 智能输入文本
     */
    static smartInput(selector, text) {
        try {
            // 先点击输入框
            if (!this.smartClick(selector)) {
                return false;
            }
            
            this.sleep(CONFIG.WAIT_TIMES.INPUT_DELAY);
            
            // 输入文本
            setText(text);
            this.log('INFO', `输入文本成功: ${text}`);
            
            this.sleep(CONFIG.WAIT_TIMES.INPUT_DELAY);
            return true;
            
        } catch (error) {
            this.log('ERROR', `输入文本失败: ${error.message}`);
            return false;
        }
    }
}

// 抖音自动加好友主类
class DouyinAutoFriend {
    constructor() {
        this.isRunning = false;
    }

    /**
     * 初始化脚本
     */
    init() {
        // 请求无障碍服务权限
        if (!auto.service) {
            Utils.log('WARN', '请开启无障碍服务');
            auto.waitFor();
        }

        // 请求截图权限
        if (!requestScreenCapture()) {
            Utils.log('ERROR', '请授予截图权限');
            exit();
        }

        // 设置屏幕方向
        setScreenMetrics(1080, 1920, 320);
        
        Utils.log('INFO', '脚本初始化完成');
    }

    /**
     * 启动抖音应用
     */
    launchDouyin() {
        Utils.log('INFO', '开始启动抖音应用');
        
        // 检查是否已经在抖音应用中
        if (currentPackage() === CONFIG.DOUYIN_PACKAGE) {
            Utils.log('INFO', '抖音应用已在运行');
            return true;
        }

        // 启动抖音应用
        try {
            app.launch(CONFIG.DOUYIN_PACKAGE);
            Utils.sleep(CONFIG.WAIT_TIMES.APP_LAUNCH);
            
            if (currentPackage() === CONFIG.DOUYIN_PACKAGE) {
                Utils.log('INFO', '抖音应用启动成功');
                
                // 处理可能的弹窗
                this.handlePopups();
                return true;
            } else {
                Utils.log('ERROR', '抖音应用启动失败');
                return false;
            }
        } catch (error) {
            Utils.log('ERROR', `启动抖音应用异常: ${error.message}`);
            return false;
        }
    }

    /**
     * 处理启动时的弹窗
     */
    handlePopups() {
        const popupTexts = ['我知道了', '确定', '允许', '同意', '跳过', '以后再说', '取消'];
        
        for (let popup of popupTexts) {
            if (textContains(popup).exists()) {
                Utils.smartClick(popup);
                Utils.sleep(1000);
            }
        }
    }

    /**
     * 导航到添加好友页面
     */
    navigateToAddFriend() {
        Utils.log('INFO', '导航到添加好友页面');
        
        try {
            // 点击"我"标签
            const meTexts = ['我', '个人中心'];
            let meFound = false;
            
            for (let text of meTexts) {
                if (Utils.smartClick(text)) {
                    meFound = true;
                    break;
                }
            }
            
            if (!meFound) {
                Utils.log('ERROR', '无法找到个人中心入口');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.PAGE_LOAD);

            // 查找并点击添加好友按钮
            const addFriendTexts = ['添加朋友', '添加好友', '发现朋友', '找朋友'];
            let found = false;
            
            for (let text of addFriendTexts) {
                if (Utils.smartClick(text)) {
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                Utils.log('ERROR', '无法找到添加好友入口');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.PAGE_LOAD);
            Utils.log('INFO', '成功进入添加好友页面');
            return true;
            
        } catch (error) {
            Utils.log('ERROR', `导航到添加好友页面失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 搜索指定抖音号
     */
    searchUser(douyinId) {
        Utils.log('INFO', `搜索用户: ${douyinId}`);
        
        try {
            // 查找搜索框
            const searchTexts = ['搜索用户', '搜索', '输入抖音号', '搜索抖音号', '请输入用户名'];
            let searchFound = false;
            
            for (let text of searchTexts) {
                if (Utils.waitForElement(text, 5000)) {
                    if (Utils.smartInput(text, douyinId)) {
                        searchFound = true;
                        break;
                    }
                }
            }
            
            if (!searchFound) {
                Utils.log('ERROR', '无法找到搜索框');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.INPUT_DELAY);
            
            // 点击搜索按钮或按回车
            if (!Utils.smartClick('搜索')) {
                // 尝试按回车键
                key(66); // KEYCODE_ENTER
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.SEARCH_RESULT);
            Utils.log('INFO', '搜索完成');
            return true;
            
        } catch (error) {
            Utils.log('ERROR', `搜索用户失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 添加好友
     */
    addFriend() {
        Utils.log('INFO', '开始添加好友');
        
        try {
            // 等待搜索结果加载
            Utils.sleep(CONFIG.WAIT_TIMES.SEARCH_RESULT);
            
            // 检查是否已经是好友
            if (textContains('已关注').exists() || textContains('互相关注').exists()) {
                Utils.log('INFO', '用户已经是好友');
                return true;
            }
            
            // 检查账号是否存在
            if (textContains('暂无相关用户').exists() || textContains('用户不存在').exists()) {
                Utils.log('ERROR', '账号不存在');
                return false;
            }
            
            // 点击关注按钮
            const followTexts = ['关注', '+ 关注', '添加好友'];
            let followSuccess = false;
            
            for (let text of followTexts) {
                if (Utils.smartClick(text)) {
                    followSuccess = true;
                    break;
                }
            }
            
            if (!followSuccess) {
                Utils.log('ERROR', '无法找到关注按钮');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.NETWORK_REQUEST);
            
            // 验证是否关注成功
            if (textContains('已关注').exists()) {
                Utils.log('INFO', '关注成功');
                return true;
            } else {
                Utils.log('WARN', '关注状态未确认，继续执行');
                return true;
            }
            
        } catch (error) {
            Utils.log('ERROR', `添加好友失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 进入用户主页
     */
    enterUserProfile() {
        Utils.log('INFO', '进入用户主页');
        
        try {
            // 点击用户头像或用户名进入主页
            if (className('ImageView').exists()) {
                className('ImageView').findOne().click();
            } else {
                // 尝试点击第一个搜索结果
                click(540, 400); // 屏幕中央位置
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.PAGE_LOAD);
            Utils.log('INFO', '成功进入用户主页');
            return true;
            
        } catch (error) {
            Utils.log('ERROR', `进入用户主页失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 发送私信
     */
    sendPrivateMessage() {
        Utils.log('INFO', '开始发送私信');
        
        try {
            // 查找并点击私信按钮
            const messageTexts = ['私信', '发消息', '聊天'];
            let messageFound = false;
            
            for (let text of messageTexts) {
                if (Utils.smartClick(text)) {
                    messageFound = true;
                    break;
                }
            }
            
            if (!messageFound) {
                Utils.log('ERROR', '无法找到私信按钮');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.PAGE_LOAD);
            
            // 输入消息内容
            const inputTexts = ['说点什么...', '输入消息', '发送消息', '请输入内容'];
            let inputSuccess = false;
            
            for (let text of inputTexts) {
                if (Utils.smartInput(text, CONFIG.MESSAGE_CONTENT)) {
                    inputSuccess = true;
                    break;
                }
            }
            
            if (!inputSuccess) {
                Utils.log('ERROR', '无法输入消息内容');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.INPUT_DELAY);
            
            // 点击发送按钮
            if (!Utils.smartClick('发送')) {
                Utils.log('ERROR', '无法找到发送按钮');
                return false;
            }
            
            Utils.sleep(CONFIG.WAIT_TIMES.NETWORK_REQUEST);
            Utils.log('INFO', '私信发送成功');
            return true;
            
        } catch (error) {
            Utils.log('ERROR', `发送私信失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 执行完整的自动化流程
     */
    async run() {
        if (this.isRunning) {
            Utils.log('WARN', '脚本正在运行中');
            return;
        }
        
        this.isRunning = true;
        Utils.log('INFO', '开始执行抖音自动加好友脚本');
        
        try {
            // 初始化
            this.init();
            
            // 1. 启动抖音应用
            if (!this.launchDouyin()) {
                throw new Error('启动抖音应用失败');
            }
            
            // 2. 导航到添加好友页面
            if (!this.navigateToAddFriend()) {
                throw new Error('导航到添加好友页面失败');
            }
            
            // 3. 搜索指定用户
            if (!this.searchUser(CONFIG.TARGET_DOUYIN_ID)) {
                throw new Error('搜索用户失败');
            }
            
            // 4. 添加好友
            if (!this.addFriend()) {
                throw new Error('添加好友失败');
            }
            
            // 5. 进入用户主页
            if (!this.enterUserProfile()) {
                throw new Error('进入用户主页失败');
            }
            
            // 6. 发送私信
            if (!this.sendPrivateMessage()) {
                throw new Error('发送私信失败');
            }
            
            Utils.log('INFO', '脚本执行完成！');
            toast('抖音自动加好友脚本执行完成！');
            
        } catch (error) {
            Utils.log('ERROR', `脚本执行失败: ${error.message}`);
            toast(`脚本执行失败: ${error.message}`);
        } finally {
            this.isRunning = false;
        }
    }
}

// 主程序入口
function main() {
    const autoFriend = new DouyinAutoFriend();
    autoFriend.run();
}

// 启动脚本
main();
