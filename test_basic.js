/**
 * 抖音自动加好友脚本 - 基础功能测试
 * Auto.js 6 Basic Test Script
 */

"ui";

// 基础测试函数
function runBasicTests() {
    console.log('🚀 开始基础功能测试');
    console.log('='.repeat(40));
    
    let passCount = 0;
    let totalTests = 0;
    
    // 测试1: 权限检查
    totalTests++;
    console.log('\n📋 测试1: 权限检查');
    try {
        if (!auto.service) {
            console.log('❌ 无障碍服务未开启');
            console.log('请在设置中开启Auto.js的无障碍服务权限');
        } else {
            console.log('✅ 无障碍服务已开启');
            passCount++;
        }
    } catch (error) {
        console.log(`❌ 权限检查失败: ${error.message}`);
    }
    
    // 测试2: 截图权限
    totalTests++;
    console.log('\n📸 测试2: 截图权限检查');
    try {
        if (requestScreenCapture()) {
            console.log('✅ 截图权限获取成功');
            passCount++;
        } else {
            console.log('❌ 截图权限获取失败');
        }
    } catch (error) {
        console.log(`❌ 截图权限测试失败: ${error.message}`);
    }
    
    // 测试3: 抖音应用检测
    totalTests++;
    console.log('\n📱 测试3: 抖音应用检测');
    try {
        const douyinPackage = "com.ss.android.ugc.aweme";
        const packages = context.getPackageManager().getInstalledPackages(0);
        let found = false;
        
        for (let i = 0; i < packages.size(); i++) {
            if (packages.get(i).packageName === douyinPackage) {
                found = true;
                break;
            }
        }
        
        if (found) {
            console.log('✅ 抖音应用已安装');
            passCount++;
        } else {
            console.log('❌ 抖音应用未安装');
        }
    } catch (error) {
        console.log(`❌ 抖音应用检测失败: ${error.message}`);
    }
    
    // 测试4: UI元素查找功能
    totalTests++;
    console.log('\n🔍 测试4: UI元素查找功能');
    try {
        // 测试基本的UI查找功能
        const testResult = textContains('设置').exists() || 
                          descContains('设置').exists() || 
                          idContains('设置').exists();
        
        console.log('✅ UI元素查找功能正常');
        passCount++;
    } catch (error) {
        console.log(`❌ UI元素查找功能异常: ${error.message}`);
    }
    
    // 测试5: 基础操作功能
    totalTests++;
    console.log('\n⚡ 测试5: 基础操作功能');
    try {
        // 测试sleep函数
        const startTime = Date.now();
        sleep(100);
        const endTime = Date.now();
        
        if (endTime - startTime >= 100) {
            console.log('✅ 基础操作功能正常');
            passCount++;
        } else {
            console.log('❌ 基础操作功能异常');
        }
    } catch (error) {
        console.log(`❌ 基础操作功能测试失败: ${error.message}`);
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(40));
    console.log('📊 测试结果汇总');
    console.log('='.repeat(40));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过数: ${passCount}`);
    console.log(`失败数: ${totalTests - passCount}`);
    console.log(`通过率: ${Math.round(passCount / totalTests * 100)}%`);
    
    if (passCount === totalTests) {
        console.log('\n🎉 所有测试通过！脚本可以正常运行。');
        toast('✅ 基础测试全部通过');
    } else {
        console.log('\n⚠️  部分测试失败，请检查环境配置。');
        toast(`⚠️ 测试通过率: ${Math.round(passCount / totalTests * 100)}%`);
        
        // 给出建议
        console.log('\n💡 建议检查项:');
        if (!auto.service) {
            console.log('- 开启Auto.js无障碍服务权限');
        }
        console.log('- 确保抖音应用已安装');
        console.log('- 检查Android系统版本(需要10+)');
    }
}

// 配置检查函数
function checkConfiguration() {
    console.log('\n🔧 配置检查');
    console.log('-'.repeat(30));
    
    // 检查目标抖音号配置
    const targetId = "test_douyin_id"; // 这里应该从main.js读取
    if (targetId === "test_douyin_id") {
        console.log('⚠️  请修改main.js中的TARGET_DOUYIN_ID配置');
    } else {
        console.log(`✅ 目标抖音号: ${targetId}`);
    }
    
    // 检查消息内容
    const message = "你好，我看了你的作品觉得非常喜欢，可以交个朋友吗";
    console.log(`✅ 私信内容: ${message}`);
    
    console.log('\n📝 使用提醒:');
    console.log('1. 请先修改main.js中的TARGET_DOUYIN_ID');
    console.log('2. 确保目标抖音号真实存在');
    console.log('3. 建议先在小号上测试');
    console.log('4. 注意使用频率，避免被限制');
}

// 主函数
function main() {
    console.clear();
    console.log('🤖 抖音自动加好友脚本 - 基础测试');
    console.log('Auto.js 6 版本');
    console.log('技术面试任务实现');
    
    // 运行基础测试
    runBasicTests();
    
    // 检查配置
    checkConfiguration();
    
    console.log('\n🚀 测试完成！如果所有测试通过，可以运行main.js开始自动化流程。');
}

// 启动测试
main();
